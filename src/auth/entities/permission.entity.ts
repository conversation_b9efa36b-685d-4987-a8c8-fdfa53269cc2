import { BaseEntityWithoutDeleteAndVersion } from 'src/common/entities/base.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Action, ActionAbility, Resource } from '../enums/permission.enum';
import { RolePermission } from './role-permission.entity';

@Entity('permissions')
export class Permission extends BaseEntityWithoutDeleteAndVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  resource: Resource;

  @Column()
  action: Action;

  @Column({ name: 'action_ability' })
  actionAbility: ActionAbility;

  @OneToMany(() => RolePermission, (rp) => rp.permission)
  rolePermissions: RolePermission[];
}
