import { Admin } from 'src/admins/entities/admin.entity';
import {
  Colum<PERSON>,
  En<PERSON><PERSON>,
  Join<PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Role } from './role.entity';

@Entity({
  name: 'admin_roles',
})
export class AdminRole {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Admin, (admin) => admin.adminRoles, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'admin_id' })
  admin: Admin;

  @Column({ name: 'admin_id' })
  adminId: number;

  @ManyToOne(() => Role, (role) => role.adminRoles, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @Column({ name: 'role_id' })
  roleId: number;
}
