import { Injectable } from '@nestjs/common';
import { AdminRepository } from 'src/admins/repositories/admin.repository';
import { AuthLoginAdminReqDto } from 'src/auth/dtos/admin/req/auth.admin.req.dto';
import { JwtAuthPayload } from 'src/auth/interfaces/jwt-payload.interface';
import { NotFoundExc } from 'src/common/exceptions/custom.exception';
import { EncryptService } from 'src/utils/services/encrypt.service';
import { AuthCommonService } from '../common/auth.common.service';
import { AppResponseDto } from 'src/common/dtos/app-response.dto';

@Injectable()
export class AuthAdminService {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly authCommonService: AuthCommonService,
    private readonly encrpyptService: EncryptService,
  ) {}

  async login(dto: AuthLoginAdminReqDto) {
    // 1. Find admin by email
    const admin = await this.adminRepo.findOne({
      where: { email: dto.email },
    });

    if (!admin) {
      throw new NotFoundExc({
        message: ['repository.admins', 'common.notFound'],
      });
    }

    // 2. Check password
    const isPasswordMatch = this.encrpyptService.compareHash(
      dto.password,
      admin.password,
    );

    if (!isPasswordMatch) {
      throw new NotFoundExc({
        message: ['repository.admins', 'common.notFound'],
      });
    }

    const payload: JwtAuthPayload = { sub: admin.id.toString() };
    const accessToken = this.authCommonService.generateAccessToken(payload);
    const refreshToken = this.authCommonService.generateRefreshToken(payload);

    return new AppResponseDto({
      accessToken,
      refreshToken,
    });
  }
}
