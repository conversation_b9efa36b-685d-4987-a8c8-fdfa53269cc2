import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/common/repositories/base.repositories';
import { DataSource } from 'typeorm';
import { I18nPath } from '../../i18n/i18n.generated';
import { RolePermission } from '../entities/role-permission.entity';

@Injectable()
export class RolePermissionRepository extends BaseRepository<RolePermission> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(RolePermission, dataSource);
    this.entityNameI18nKey = 'repository.rolePermissions';
  }
}
