import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/common/repositories/base.repositories';
import { DataSource } from 'typeorm';
import { I18nPath } from '../../i18n/i18n.generated';
import { Permission } from '../entities/permission.entity';

@Injectable()
export class PermissionRepository extends BaseRepository<Permission> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(Permission, dataSource);
    this.entityNameI18nKey = 'repository.permissions';
  }
}
