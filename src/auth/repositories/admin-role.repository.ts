import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/common/repositories/base.repositories';
import { DataSource } from 'typeorm';
import { I18nPath } from '../../i18n/i18n.generated';
import { AdminRole } from '../entities/admin-role.entity';

@Injectable()
export class AdminRoleRepository extends BaseRepository<AdminRole> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(AdminRole, dataSource);
    this.entityNameI18nKey = 'repository.adminRoles';
  }
}
