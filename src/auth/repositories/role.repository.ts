import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/common/repositories/base.repositories';
import { DataSource } from 'typeorm';
import { I18nPath } from '../../i18n/i18n.generated';
import { Role } from '../entities/role.entity';

@Injectable()
export class RoleRepository extends BaseRepository<Role> {
  entityNameI18nKey: I18nPath;
  constructor(dataSource: DataSource) {
    super(Role, dataSource);
    this.entityNameI18nKey = 'repository.roles';
  }
}
