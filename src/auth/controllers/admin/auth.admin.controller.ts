import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { PrefixType } from 'src/common/enums/app.enum';
import { AuthLoginAdminReqDto } from '../../dtos/admin/req/auth.admin.req.dto';
import { AuthAdminService } from '../../services/admin/auth.admin.service';

@Controller(`${PrefixType.ADMIN}/auth`)
@ApiTags('Auth Admin')
export class AuthAdminController {
  constructor(private authAdminService: AuthAdminService) {}

  @Post('login')
  login(@Body() body: AuthLoginAdminReqDto) {
    return this.authAdminService.login(body);
  }
}
