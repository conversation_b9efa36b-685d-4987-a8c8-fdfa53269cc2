import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AdminRepository } from 'src/admins/repositories/admin.repository';
import { GlobalConfig } from 'src/common/configs/global.config';
import { UtilsModule } from 'src/utils/utils.module';
import { AuthAdminController } from './controllers/admin/auth.admin.controller';
import { AuthAdminService } from './services/admin/auth.admin.service';
import { AuthCommonService } from './services/common/auth.common.service';
import { JwtAuthenAdminStrategy } from './strategies/jwt-authen.admin.strategy';

@Module({
  imports: [
    HttpModule,
    PassportModule,
    JwtModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService<GlobalConfig>) => ({
        secret: configService.get('auth.accessToken.secret'),
        signOptions: {
          algorithm: configService.get('auth.accessToken.algorithm'),
        },
      }),
    }),
    UtilsModule,
  ],
  controllers: [AuthAdminController],
  providers: [
    AuthAdminService,
    AuthCommonService,
    AdminRepository,
    JwtAuthenAdminStrategy,
  ],
})
export class AuthModule {}
