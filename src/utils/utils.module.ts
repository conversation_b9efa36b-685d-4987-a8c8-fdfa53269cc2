import { Global, Module } from '@nestjs/common';
import { AdminRepository } from 'src/admins/repositories/admin.repository';
import { AdminRoleRepository } from 'src/auth/repositories/admin-role.repository';
import { PermissionRepository } from 'src/auth/repositories/permission.repository';
import { RolePermissionRepository } from 'src/auth/repositories/role-permission.repository';
import { RoleRepository } from 'src/auth/repositories/role.repository';
import { EncryptService } from './services/encrypt.service';
import { SeedService } from './services/seed.service';

@Global()
@Module({
  providers: [
    EncryptService,
    SeedService,
    AdminRepository,
    RoleRepository,
    PermissionRepository,
    AdminRoleRepository,
    RolePermissionRepository,
  ],
  exports: [EncryptService, SeedService],
})
export class UtilsModule {}
