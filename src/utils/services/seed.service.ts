import { Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { AdminStatus } from 'src/admins/enums/admin.enum';
import { AdminRepository } from 'src/admins/repositories/admin.repository';
import {
  Action,
  ActionAbility,
  Resource,
} from 'src/auth/enums/permission.enum';
import { AdminRoleRepository } from 'src/auth/repositories/admin-role.repository';
import { PermissionRepository } from 'src/auth/repositories/permission.repository';
import { RolePermissionRepository } from 'src/auth/repositories/role-permission.repository';
import { RoleRepository } from 'src/auth/repositories/role.repository';
import { EncryptService } from './encrypt.service';

@Injectable()
export class SeedService {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly roleRepo: RoleRepository,
    private readonly permRepo: PermissionRepository,
    private readonly adminRoleRepo: AdminRoleRepository,
    private readonly rolePermRepo: RolePermissionRepository,
    private readonly encrpyptService: EncryptService,
  ) {}

  async seedRootAdmin() {
    let rootRole = await this.roleRepo.findOne({ where: { name: 'root' } });
    if (!rootRole) {
      const manageAll = this.permRepo.create({
        action: Action.MANAGE,
        resource: Resource.ALL,
        actionAbility: ActionAbility.CAN,
      });
      await this.permRepo.save(manageAll);

      rootRole = this.roleRepo.create({
        name: 'root',
        description: 'Full access',
        rolePermissions: [manageAll],
      });

      await this.roleRepo.save(rootRole);
      await this.rolePermRepo.save({
        roleId: rootRole.id,
        permissionId: manageAll.id,
      });
    }

    const existingRoot = await this.adminRepo.findOne({
      where: { email: '<EMAIL>' },
    });
    if (!existingRoot) {
      const hashedPassword = this.encrpyptService.encryptText('root');
      const rootAdmin = this.adminRepo.create({
        name: 'Root',
        email: '<EMAIL>',
        password: hashedPassword,
        adminRoles: [rootRole],
        status: AdminStatus.ACTIVE,
      });

      await this.adminRepo.save(rootAdmin);
      await this.adminRoleRepo.save({
        roleId: rootRole.id,
        adminId: rootAdmin.id,
      });
    }
  }
}
