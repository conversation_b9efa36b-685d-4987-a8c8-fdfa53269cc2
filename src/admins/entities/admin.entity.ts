import { AdminRole } from 'src/auth/entities/admin-role.entity';
import { BaseEntity } from 'src/common/entities/base.entity';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { AdminStatus } from '../enums/admin.enum';

@Entity('admins')
export class Admin extends BaseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({ length: 255, nullable: true })
  name: string;

  @Column({ enum: AdminStatus, type: 'enum' })
  status: AdminStatus;

  @OneToMany(() => AdminRole, (ar) => ar.admin)
  adminRoles: AdminRole[];
}
