/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "auth": {
        "invalidToken": string;
        "expiredToken": string;
    };
    "common": {
        "notFound": string;
    };
    "exception": {
        "badRequest": string;
        "internalServerError": string;
        "notFound": string;
        "forbidden": string;
        "unauthorized": string;
        "conflict": string;
        "expectationFailed": string;
        "serviceUnavailable": string;
    };
    "repository": {
        "admins": string;
        "roles": string;
        "permissions": string;
        "adminRoles": string;
        "rolePermissions": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
