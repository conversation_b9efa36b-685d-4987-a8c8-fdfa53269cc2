import {
  AbilityBuilder,
  createMongoAbility,
  MongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { Admin } from 'src/admins/entities/admin.entity';
import {
  Action,
  ActionAbility,
  Resource,
} from 'src/auth/enums/permission.enum';

export type AppAbility = MongoAbility<[Action, Resource]>;

@Injectable()
export class CaslAbilityFactory {
  createForAdmin(admin: Admin): AppAbility {
    const { can, cannot, build } = new AbilityBuilder<AppAbility>(
      createMongoAbility,
    );

    for (const adminRole of admin.adminRoles) {
      if (adminRole.role.name === 'root') {
        can(Action.MANAGE, Resource.ALL);
      }

      for (const perm of adminRole.role.rolePermissions) {
        if (perm.permission.actionAbility === ActionAbility.CANNOT) {
          cannot(
            perm.permission.action as Action,
            perm.permission.resource as Resource,
          );
        } else {
          can(
            perm.permission.action as Action,
            perm.permission.resource as Resource,
          );
        }
      }
    }

    return build();
  }
}
