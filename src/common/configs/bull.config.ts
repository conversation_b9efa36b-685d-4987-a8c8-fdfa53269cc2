import { SharedBullAsyncConfiguration } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import Redis, { RedisOptions } from 'ioredis';
import { AppEnvironment } from '../enums/app.enum';
import { GlobalConfig } from './global.config';

export const bullConfig: SharedBullAsyncConfiguration = {
  inject: [ConfigService],
  useFactory(configService: ConfigService<GlobalConfig>) {
    return {
      createClient(type, redisOpts: RedisOptions) {
        const opts: RedisOptions = {
          ...redisOpts,
          ...(type !== 'client'
            ? { enableReadyCheck: false, maxRetriesPerRequest: null }
            : {}),
        };

        const redisHost = configService.get('redis.host');
        const redisPort = configService.get('redis.port');
        const redisPassword = configService.get('redis.password');

        let redisConfig: RedisOptions;

        switch (configService.get('environment')) {
          case AppEnvironment.TEST:
            redisConfig = {
              ...opts,
              host: '*********',
              port: 6379,
              password: redisPassword,
            };
            break;
          default:
            redisConfig = {
              ...opts,
              host: redisHost,
              port: Number(redisPort),
              password: redisPassword,
            };
            break;
        }

        return new Redis(redisConfig);
      },
      defaultJobOptions: { removeOnComplete: true, removeOnFail: true },
    };
  },
};
